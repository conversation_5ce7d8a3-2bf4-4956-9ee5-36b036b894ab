<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MeshLine 3D Animation</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
            background-color: #101020;
            overflow: hidden;
            color: white;
        }
        
        #container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }
        
        #controls {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 100;
            background: rgba(0, 0, 0, 0.7);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        
        .control-group {
            margin-bottom: 15px;
        }
        
        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 14px;
            color: #ccc;
        }
        
        .control-group input[type="range"] {
            width: 200px;
            margin-right: 10px;
        }
        
        .control-group span {
            font-size: 12px;
            color: #999;
        }
        
        #canvas {
            display: block;
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="controls">
            <div class="control-group">
                <label for="dash">虚线比例 (Dash Ratio)</label>
                <input type="range" id="dash" min="0" max="0.99" step="0.01" value="0.9">
                <span id="dashValue">0.9</span>
            </div>
            <div class="control-group">
                <label for="count">线条数量 (Line Count)</label>
                <input type="range" id="count" min="0" max="200" step="1" value="50">
                <span id="countValue">50</span>
            </div>
            <div class="control-group">
                <label for="radius">半径范围 (Radius)</label>
                <input type="range" id="radius" min="1" max="100" step="1" value="50">
                <span id="radiusValue">50</span>
            </div>
        </div>
        <canvas id="canvas"></canvas>
    </div>

    <!-- Three.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/0.174.0/three.min.js"></script>
    <!-- MeshLine - 使用更稳定的CDN链接 -->
    <script src="https://unpkg.com/three.meshline@1.4.0/src/THREE.MeshLine.js"></script>
    <!-- Post-processing -->
    <script src="https://unpkg.com/postprocessing@6.35.6/build/postprocessing.umd.min.js"></script>

    <script>
        // 全局变量
        let scene, camera, renderer, composer;
        let lines = [];
        let animationId;
        
        // 控制参数
        let params = {
            dash: 0.9,
            count: 50,
            radius: 50
        };
        
        // 颜色数组
        const colors = [
            new THREE.Color(10, 0.5, 2),  // 紫红色
            new THREE.Color(1, 2, 10),    // 蓝色
            new THREE.Color("#A2CCB6"),   // 淡绿色
            new THREE.Color("#FCEEB5"),   // 淡黄色
            new THREE.Color("#EE786E"),   // 粉红色
            new THREE.Color("#e0feff")    // 淡蓝色
        ];
        
        // 鼠标位置
        const mouse = new THREE.Vector2();
        
        // 初始化场景
        function init() {
            // 创建场景
            scene = new THREE.Scene();
            scene.background = new THREE.Color('#101020');
            
            // 创建相机
            camera = new THREE.PerspectiveCamera(90, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(0, 0, 5);
            
            // 创建渲染器
            renderer = new THREE.WebGLRenderer({ 
                canvas: document.getElementById('canvas'),
                antialias: true 
            });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setPixelRatio(window.devicePixelRatio);
            
            // 设置后期处理
            setupPostProcessing();
            
            // 创建线条
            createLines();
            
            // 设置控制面板
            setupControls();
            
            // 设置鼠标事件
            setupMouseEvents();
            
            // 开始动画循环
            animate();
        }
        
        // 设置后期处理
        function setupPostProcessing() {
            if (typeof POSTPROCESSING !== 'undefined') {
                composer = new POSTPROCESSING.EffectComposer(renderer);
                composer.addPass(new POSTPROCESSING.RenderPass(scene, camera));
                
                const bloomEffect = new POSTPROCESSING.BloomEffect({
                    luminanceThreshold: 1,
                    radius: 0.6,
                    mipmapBlur: true
                });
                
                const effectPass = new POSTPROCESSING.EffectPass(camera, bloomEffect);
                composer.addPass(effectPass);
            }
        }
        
        // 创建线条
        function createLines() {
            // 清除现有线条
            lines.forEach(line => {
                scene.remove(line.mesh);
                line.geometry.dispose();
                line.material.dispose();
            });
            lines = [];
            
            // 创建新线条
            for (let i = 0; i < params.count; i++) {
                const line = createSingleLine();
                lines.push(line);
                scene.add(line.mesh);
            }
        }
        
        // 创建单条线
        function createSingleLine() {
            // 创建随机起始位置
            const pos = new THREE.Vector3(
                THREE.MathUtils.randFloatSpread(params.radius),
                THREE.MathUtils.randFloatSpread(params.radius),
                THREE.MathUtils.randFloatSpread(params.radius)
            );

            // 创建10个随机点
            const points = [];
            for (let i = 0; i < 10; i++) {
                pos.add(new THREE.Vector3(
                    THREE.MathUtils.randFloatSpread(params.radius),
                    THREE.MathUtils.randFloatSpread(params.radius),
                    THREE.MathUtils.randFloatSpread(params.radius)
                ));
                points.push(pos.clone());
            }

            // 使用CatmullRom曲线创建平滑曲线
            const curve = new THREE.CatmullRomCurve3(points);
            const curvePoints = curve.getPoints(300);

            // 创建MeshLine几何体和材质
            let geometry, material, mesh;

            if (typeof THREE.MeshLineGeometry !== 'undefined') {
                // 使用MeshLine库
                geometry = new THREE.MeshLineGeometry();
                geometry.setPoints(curvePoints);

                material = new THREE.MeshLineMaterial({
                    transparent: true,
                    lineWidth: Math.max(params.radius / 100, (params.radius / 50) * Math.random()),
                    color: colors[Math.floor(Math.random() * colors.length)],
                    depthWrite: false,
                    dashArray: 0.25,
                    dashRatio: params.dash,
                    toneMapped: false
                });
            } else {
                // 备用方案：使用普通的线条几何体
                geometry = new THREE.BufferGeometry().setFromPoints(curvePoints);
                material = new THREE.LineBasicMaterial({
                    color: colors[Math.floor(Math.random() * colors.length)],
                    transparent: true,
                    opacity: 0.8
                });
                mesh = new THREE.Line(geometry, material);
                return {
                    mesh: mesh,
                    geometry: geometry,
                    material: material,
                    speed: Math.max(0.1, Math.random())
                };
            }

            // 创建网格
            mesh = new THREE.Mesh(geometry, material);

            return {
                mesh: mesh,
                geometry: geometry,
                material: material,
                speed: Math.max(0.1, Math.random())
            };
        }
        
        // 设置控制面板
        function setupControls() {
            const dashSlider = document.getElementById('dash');
            const countSlider = document.getElementById('count');
            const radiusSlider = document.getElementById('radius');
            
            const dashValue = document.getElementById('dashValue');
            const countValue = document.getElementById('countValue');
            const radiusValue = document.getElementById('radiusValue');
            
            dashSlider.addEventListener('input', (e) => {
                params.dash = parseFloat(e.target.value);
                dashValue.textContent = params.dash;
                updateDashRatio();
            });
            
            countSlider.addEventListener('input', (e) => {
                params.count = parseInt(e.target.value);
                countValue.textContent = params.count;
                createLines();
            });
            
            radiusSlider.addEventListener('input', (e) => {
                params.radius = parseInt(e.target.value);
                radiusValue.textContent = params.radius;
                createLines();
            });
        }
        
        // 更新虚线比例
        function updateDashRatio() {
            lines.forEach(line => {
                line.material.dashRatio = params.dash;
            });
        }
        
        // 设置鼠标事件
        function setupMouseEvents() {
            document.addEventListener('mousemove', (event) => {
                mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
                mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
            });
        }
        
        // 动画循环
        function animate() {
            animationId = requestAnimationFrame(animate);
            
            const time = performance.now() * 0.001;
            
            // 更新线条动画
            lines.forEach(line => {
                line.material.dashOffset -= 0.01 * line.speed;
            });
            
            // 更新相机位置（基于鼠标位置）
            const radius = 20;
            const targetX = Math.sin(mouse.x) * radius;
            const targetY = Math.atan(mouse.y) * radius;
            const targetZ = Math.cos(mouse.x) * radius;
            
            // 平滑相机移动
            camera.position.lerp(new THREE.Vector3(targetX, targetY, targetZ), 0.05);
            camera.lookAt(0, 0, 0);
            
            // 渲染
            if (composer) {
                composer.render();
            } else {
                renderer.render(scene, camera);
            }
        }
        
        // 窗口大小调整
        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
            if (composer) {
                composer.setSize(window.innerWidth, window.innerHeight);
            }
        }
        
        // 事件监听
        window.addEventListener('resize', onWindowResize);
        
        // 页面加载完成后初始化
        window.addEventListener('load', init);
        
        // 页面卸载时清理资源
        window.addEventListener('beforeunload', () => {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            lines.forEach(line => {
                line.geometry.dispose();
                line.material.dispose();
            });
        });
    </script>
</body>
</html>
